generator client {
  provider        = "prisma-client-js"
  previewFeatures = ["driverAdapters"]
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

enum PostType {
  ARTICLE
  ORGARTICLE
  VIDEOYT
  VIDEOOTHER
}

model Tags {
  id        Int @id @default(autoincrement())
  name      String
  articles  ArticleTag[]
  @@map("tags")
}

model Article {
  id             Int      @id @default(autoincrement())
  title          String
  post_type      PostType @default(ARTICLE)
  content        String
  summary        String?
  summarization_vendor String?
  slug           String
  url            String   @unique
  author         String?
  source_id      Int
  source         Source?  @relation(fields: [source_id], references: [id])
  tags           String[]
  article_tags   ArticleTag[]
  published_date DateTime
  thumbnail_key  String?
  views          Int?
  video_id       String?

  // createdAt   DateTime @default(now())
  @@index([source_id])
  @@index([published_date])
  @@map("articles")
}

model ArticleTag {
  id         Int     @id @default(autoincrement())
  article_id Int
  tag_id     Int
  article    Article @relation(fields: [article_id], references: [id], onDelete: Cascade)
  tag        Tags    @relation(fields: [tag_id], references: [id], onDelete: Cascade)

  @@unique([article_id, tag_id])
  @@map("article_tags")
}

model Source {
  id       Int       @id @default(autoincrement())
  title    String
  site     String
  rss_url  String
  articles Article[]

  @@map("sources")
}
