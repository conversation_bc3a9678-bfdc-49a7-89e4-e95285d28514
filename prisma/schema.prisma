generator client {
  provider        = "prisma-client-js"
  previewFeatures = ["driverAdapters"]
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

enum PostType {
  ARTICLE
  ORGARTICLE
  VIDEOYT
  VIDEOOTHER
}

model Article {
  id                   Int      @id @default(autoincrement())
  title                String
  post_type            PostType @default(ARTICLE)
  content              Json     // Changed to <PERSON>son for Payload's richText
  summary              String?  @db.VarChar // Changed to VarChar for Payload's textarea
  summarization_vendor String?
  slug                 String
  url                  String   @unique
  author               String?
  source_id            Int
  source               Source?  @relation(fields: [source_id], references: [id])
  tags                 String?  // Keep as text field to preserve existing comma-separated tags
  published_date       DateTime @db.Timestamptz(3) // Changed to match Payload's date type
  thumbnail_key        String?
  views                Decimal? // Changed to Decimal for Payload's number type
  video_id             String?

  // Payload adds these automatically
  updated_at           DateTime @default(now()) @updatedAt @map("updated_at")
  created_at           DateTime @default(now()) @map("created_at")

  @@index([source_id])
  @@index([published_date])
  @@map("articles")
}

model Source {
  id       Int       @id @default(autoincrement())
  title    String
  site     String
  rss_url  String
  articles Article[]

  @@map("sources")
}
