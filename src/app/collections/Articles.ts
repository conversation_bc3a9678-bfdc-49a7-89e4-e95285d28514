import type { CollectionConfig } from 'payload'

const formatSlug = (val: string): string =>
  val
    .replace(/ /g, '-')
    .replace(/[^\w-]+/g, '')
    .toLowerCase()

export const Articles: CollectionConfig = {
  slug: 'articles',
  admin: {
    useAsTitle: 'title',
    defaultColumns: ['title', 'source', 'published_date', 'post_type'],
  },
  access: {
    read: () => true,
  },
  hooks: {
    beforeChange: [
      ({ data }) => {
        if (data.title && !data.slug) {
          data.slug = formatSlug(data.title)
        }
        return data
      },
    ],
  },
  fields: [
    {
      name: 'title',
      type: 'text',
      required: true,
      admin: {
        description: 'The title of the article',
      },
    },
    {
      name: 'post_type',
      type: 'select',
      required: true,
      defaultValue: 'ARTICLE',
      options: [
        {
          label: 'Article',
          value: 'ARTICLE',
        },
        {
          label: 'YouTube Video',
          value: 'VIDEOYT',
        },
        {
          label: 'Other Video',
          value: 'VIDEOOTHER',
        },
      ],
    },
    {
      name: 'content',
      type: 'richText',
      required: true,
      admin: {
        description: 'The main content of the article',
      },
    },
    {
      name: 'summary',
      type: 'textarea',
      admin: {
        description: 'A brief summary or TLDR of the article',
      },
    },
    {
      name: 'summarization_vendor',
      type: 'text',
      admin: {
        description: 'The AI service used to generate the summary',
      },
    },
    {
      name: 'slug',
      type: 'text',
      required: true,
      unique: true,
      admin: {
        description: 'URL-friendly version of the title',
        position: 'sidebar',
      },
      hooks: {
        beforeValidate: [
          ({ value, data }) => {
            if (!value && data?.title) {
              return formatSlug(data.title)
            }
            return value
          },
        ],
      },
    },
    {
      name: 'url',
      type: 'text',
      required: true,
      unique: true,
      admin: {
        description: 'Original URL of the article',
      },
    },
    {
      name: 'author',
      type: 'text',
      admin: {
        description: 'Author of the article',
      },
    },
    {
      name: 'source',
      type: 'relationship',
      relationTo: 'sources',
      required: true,
      admin: {
        description: 'The source publication for this article',
      },
    },
    {
      name: 'tags',
      type: 'array',
      fields: [
        {
          name: 'tag',
          type: 'text',
        },
      ],
      admin: {
        description: 'Tags associated with the article',
      },
    },
    {
      name: 'published_date',
      type: 'date',
      required: true,
      admin: {
        description: 'When the article was originally published',
        date: {
          pickerAppearance: 'dayAndTime',
        },
      },
    },
    {
      name: 'thumbnail',
      type: 'relationship',
      relationTo: 'media',
      admin: {
        description: 'Thumbnail image for the article',
      },
    },
    {
      name: 'thumbnail_key',
      type: 'text',
      admin: {
        description: 'URL or key for the article thumbnail image (fallback)',
      },
    },
    {
      name: 'views',
      type: 'number',
      defaultValue: 0,
      admin: {
        description: 'Number of times the article has been viewed',
      },
    },
    {
      name: 'video_id',
      type: 'text',
      admin: {
        description: 'Video ID for video content types',
        condition: (data) => data.post_type === 'VIDEOYT' || data.post_type === 'VIDEOOTHER',
      },
    },
    {
      name: 'status',
      type: 'select',
      required: true,
      defaultValue: 'draft',
      options: [
        {
          label: 'Draft',
          value: 'draft',
        },
        {
          label: 'Published',
          value: 'published',
        },
        {
          label: 'Archived',
          value: 'archived',
        },
      ],
      admin: {
        description: 'Publication status of the article',
        position: 'sidebar',
      },
    },
    {
      name: 'featured',
      type: 'checkbox',
      defaultValue: false,
      admin: {
        description: 'Mark as featured article',
        position: 'sidebar',
      },
    },
  ],
}
