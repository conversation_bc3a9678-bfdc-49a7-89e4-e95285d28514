'use client';

import { Heart, MessageCircle, Share2, Bookmark, Image as ImageIcon } from 'lucide-react';
import { useState, useEffect, useRef } from 'react';
import { useRouter } from "next/navigation";
import Link from 'next/link';
import ArticleSkeleton from '../../components/ArticleSkeleton';
import { Article } from '@/app/(frontend)/utils/article'

import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";

import { Badge} from "@/components/ui/badge";
import {useRedirect} from "@/hooks/useRedirect";

export default function Home() {
  const [activeFilter, setActiveFilter] = useState('Latest');
  const [showThumbnails, setShowThumbnails] = useState(true);
  const [articles, setArticles] = useState<Article[]>([]);
  const [page, setPage] = useState(1);
  const [loading, setLoading] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const observerTarget = useRef<HTMLDivElement>(null);
  const [selectedArticle, setSelectedArticle] = useState<Article | null>(null);

  const fetchArticles = async (pageNum: number) => {
    try {
      setLoading(true);
      const response = await fetch(`/api/articles?page=${pageNum}&limit=10&filter=${activeFilter}`);
      const data = await response.json();
      
      setArticles(prev => pageNum === 1 ? data.articles : [...prev, ...data.articles]);
      setHasMore(data.pagination.hasMore);

    } catch (error) {
      console.error('Error fetching articles:', error);
    } finally {
      setLoading(false);
    }
  };


  const handleClickAction = (article: Article) => {
    // router.push('article/' + article.slug)
    window.history.pushState({}, "", window.location.protocol + '//' + window.location.host + '/article/'+ article.slug )
    setSelectedArticle(article);
  }

  useEffect(() => {
    setPage(1);
    fetchArticles(1);
  }, [activeFilter]);

  useEffect(() => {
    const observer = new IntersectionObserver(
      entries => {
        if (entries[0].isIntersecting && hasMore && !loading) {
          setPage(prev => prev + 1);
        }
      },
      { threshold: 1.0 }
    );

    if (observerTarget.current) {
      observer.observe(observerTarget.current);
    }

    return () => observer.disconnect();
  }, [hasMore, loading]);

  useEffect(() => {
    if (page > 1) {
      fetchArticles(page);
    }
  }, [page]);


  return (
    <div className="max-w-[2000px] mx-auto p-6">
      {/* Controls */}
      <div className="flex justify-between items-center mb-8">
        <div className="flex gap-4">
          {['Latest', 'Popular'].map((filter) => (
            <button
              key={filter}
              onClick={() => setActiveFilter(filter)}
              className={`px-4 py-2 rounded-lg font-medium transition-all duration-200 ${
                activeFilter === filter
                  ? 'bg-blue-500 text-white shadow-md'
                  : 'bg-white dark:bg-gray-800 text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'
              }`}
            >
              {filter}
            </button>
          ))}
        </div>
        <button
          onClick={() => setShowThumbnails(!showThumbnails)}
          className="px-4 py-2 rounded-lg font-medium bg-white dark:bg-gray-800 text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 flex items-center gap-2"
        >
          <ImageIcon className="w-5 h-5" />
          {showThumbnails ? 'Hide' : 'Show'} <span className="max-sm:hidden">Thumbnails</span>
        </button>
      </div>

      {/* News Grid */}
      <div className="grid grid-cols-1  md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5 gap-6">
        {articles.map((article) => (
          <article
            key={article.id}
            className="bg-white cursor-pointer dark:bg-gray-800 rounded-xl shadow-md hover:shadow-lg transition-shadow duration-300 overflow-hidden group"
            onClick={() => handleClickAction(article)}
          >
            <div className="block">
              {showThumbnails && article.thumbnail_key && (
                <div onClick={() => handleClickAction(article)}  /* href={`/article/${slugify(article.title)}`} */  className="block">
                  <div className="aspect-video w-full overflow-hidden">
                    <img
                      src={article.thumbnail_key}
                      alt={article.title}
                      className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                    />
                  </div>
                </div>
              )}
              <div className="p-4">
                <Link 
                  href={`/tag/${encodeURIComponent(article.category)}`}
                  className="inline-block px-3 py-1 rounded-full text-sm font-medium bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 mb-3 hover:bg-blue-200 dark:hover:bg-blue-800 transition-colors"
                >
                  {article.category}
                </Link>

              <div /* ref={`/article/${article.url}`} */ >
                  <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-2 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors duration-200 line-clamp-2">
                    {article.title}
                  </h2>

                  <p className="text-gray-600 dark:text-gray-300 mb-4 text-sm line-clamp-2">
                    {article.summary}
                  </p>
              </div>

                {/* Metadata and Actions */}
                <div className="flex items-center justify-between text-sm text-gray-500 dark:text-gray-400">
                  <div className="flex items-center space-x-4">
                    <span>{article.source?.title}</span>
                    <span>•</span>
                    <span>{article.timestamp}</span>
                  </div>

                  {/* Engagement Metrics */}
                  <div className="flex items-center space-x-4">
                    <button className="flex items-center space-x-1 hover:text-blue-500 transition-colors">
                      <Heart className="w-4 h-4" />
                      <span>{article.likes}</span>
                    </button>
                    <button className="flex items-center space-x-1 hover:text-blue-500 transition-colors">
                      <MessageCircle className="w-4 h-4" />
                      <span>{article.comments}</span>
                    </button>
                    <button className="hover:text-blue-500 transition-colors">
                      <Share2 className="w-4 h-4" />
                    </button>
                    <button className="hover:text-blue-500 transition-colors">
                      <Bookmark className="w-4 h-4" />
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </article>
        ))}

        {/* Loading Skeletons */}
        {loading && (
          <>
            <ArticleSkeleton />
            <ArticleSkeleton />
            <ArticleSkeleton />
            <ArticleSkeleton />
            <ArticleSkeleton />
          </>
        )}
      </div>

      {/* Intersection Observer Target */}
      <div ref={observerTarget} className="h-4 mt-8" />

      {/* End of Content Message */}
      {!hasMore && !loading && articles.length > 0 && (
        <p className="text-center text-gray-500 dark:text-gray-400 mt-8">
          No more articles to load
        </p>
      )}

      <Dialog open={!!selectedArticle} onOpenChange={(open) => !open && setSelectedArticle(null)}>
        <DialogContent className="sm:max-w-[725px]">
          <DialogHeader>

            <Link href={ useRedirect(selectedArticle?.slug) ?? ''} target="_blank">
            <div className="flex items-center aspect-video relative mb-4">
              <div className="bg-gray-100 p-2 rounded-lg">
                <img
                    src={selectedArticle?.thumbnail_key}
                    alt={selectedArticle?.title}
                    //  fill
                    className="object-cover rounded-lg"
                />
              </div>
            </div>

            </Link>

            <DialogTitle>{selectedArticle?.title}</DialogTitle>
          </DialogHeader>
          <div className="mt-4 text-sm text-gray-600">
            <p>
              <span className="font-semibold">TLDR</span>
              <br />
              {selectedArticle?.summary}
            </p>
            <div className="mt-2">
              <Badge variant="secondary" className="text-gray-500">{selectedArticle?.category}</Badge>
            </div>
          </div>
          <div className="mt-4 prose">

          </div>
        </DialogContent>
      </Dialog>


    </div>

  );
}
