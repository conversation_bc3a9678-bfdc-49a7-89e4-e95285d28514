import {prisma} from '@/db/prisma'
import {Prisma} from '@prisma/client'

export interface Article {
    id: string;
    slug: string;
    url: string;
    category: string;
    title: string;
    content: string;
    summary?: string;
    summarization_vendor?: string;
    source_id: number;
    source: { title: string};
    timestamp: string;
    likes: number;
    comments: number;
    thumbnail_key?: string;
}

const POSTS_PER_PAGE = 50;

export async function getArticleBySlug(slug: string) {
    try {
        return await prisma.article.findFirst({
            where: {
                slug: slug
            },
            include: {
                source: {
                    select: { title: true}
                }
            }
        });
    }
    catch (error) {
        console.error(error)
        return null
    }
}

export async function getLatestArticles(page : number = 1, filter?: string, source_id?: string) {
    try {

        let sortBy = {}
        if (filter === 'Popular') {
            sortBy = {
                views: "desc"
            }
        }

        const articles =  await prisma.article.findMany({
            where: source_id ? { source_id: parseInt(source_id) } : undefined,
            // relationLoadStrategy: 'join',
            take: POSTS_PER_PAGE,
            orderBy: [
                {
                    published_date: 'desc',
                },
                {
                    ...sortBy
                }
            ],
            skip: (page  - 1) * POSTS_PER_PAGE,
            include: {
                source: {
                    select: {
                        title: true
                    }
                }
            }
        })

       //  const articles = []
        const totalCount = await prisma.article.count({
             where: source_id ? { source_id: parseInt(source_id) } : undefined
        })

        return {
            articles,
            pagination: {
                page,
                total: Math.ceil(totalCount / POSTS_PER_PAGE),
                hasMore: page * POSTS_PER_PAGE < totalCount
            }
        }

    } catch (error) {
        console.log(error.code)
        if (error instanceof Prisma.PrismaClientKnownRequestError) {
            console.log(error.code)
            if (error.code === 'P101') {
                console.log(
                    'Can\'t reach database serve'
                )
            }
        }
        return error;
    }
}

export async function incrViews(post_id: number) {
    try {
        return await prisma.article.update({
            where: { id: post_id },
            data: {views: {increment: 1}}
        })
    }
    catch(error) {
        console.error('Error', error)
        return null;
    }
}
