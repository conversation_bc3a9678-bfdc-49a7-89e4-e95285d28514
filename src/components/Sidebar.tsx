'use client';

import { sources, trendingTopics } from '../data/news';
import {Hash, Menu, X} from 'lucide-react';
import {useEffect, useState} from "react";

export default function Sidebar() {
  const [isOpen, setIsOpen] = useState(true);
  const [isMobile, setIsMobile] = useState(false)

  const toggleSidebar = () => {
    setIsOpen(!isOpen);
  };

  useEffect(() => {
    const checkScreenSize = () => {
      setIsMobile(window.innerWidth < 768);
      if (window.innerWidth < 768) {
        setIsOpen(false);
      } else {
        setIsOpen(true);
      }
    }

    checkScreenSize();
    window.addEventListener('resize', checkScreenSize);
    return () => window.removeEventListener('resize', checkScreenSize);

  }, []);

  return (
  <>
      <button
          onClick={toggleSidebar}
          className="md:hidden fixed top-4 left-4 z-50 p-2 rounded-md bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300"
      >
        {isOpen ? <X className="w-5 h-5" /> : <Menu className="w-5 h-5" />}
      </button>

      <aside className={`${
          isOpen ? 'translate-x-0' : '-translate-x-full'
        } w-64 h-screen fixed left-0 top-0 bg-white dark:bg-gray-900 border-r border-gray-200 dark:border-gray-800 p-4 overflow-y-auto`}>

        <div className="space-y-8">
          {/* Sources Section */}
          <div>
            <h2 className="text-lg font-semibold mb-4 text-gray-900 dark:text-white">Sources</h2>
            <ul className="space-y-2">
              {sources.map((source) => (
                <li key={source.id}>
                  <button className="w-full text-left px-4 py-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 text-gray-700 dark:text-gray-300 transition-colors">
                    <span className="mr-2"><img className='h-4 float-left' src={source.iconUrl} alt=""/></span>
                    {source.name}
                  </button>
                </li>
              ))}
            </ul>
          </div>

          {/* Trending Topics */}
          <div>
            <h2 className="text-lg font-semibold mb-4 text-gray-900 dark:text-white">Trending</h2>
            <ul className="space-y-2">
              {trendingTopics.map((topic) => (
                <li key={topic}>
                  <button className="w-full text-left px-4 py-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 text-gray-700 dark:text-gray-300 transition-colors">
                    <Hash className="inline w-4 h-4 mr-2" />
                    {topic}
                  </button>
                </li>
              ))}
            </ul>
          </div>


        </div>
    </aside>
  </>
  );
}
